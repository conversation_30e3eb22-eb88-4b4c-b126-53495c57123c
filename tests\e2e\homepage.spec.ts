import { test, expect } from "@playwright/test";

test.describe("Homepage", () => {
  test("should load homepage successfully", async ({ page }) => {
    await page.goto("/");

    // Check if page loads
    await expect(page).toHaveTitle(/InulPRO/i);

    // Check for main navigation or header
    await expect(page.locator("nav")).toBeVisible();
  });

  test("should display course listings", async ({ page }) => {
    await page.goto("/");

    // Wait for courses to load
    await page.waitForLoadState("networkidle");

    // Check if courses section exists
    const coursesSection = page.locator('[data-testid="courses-section"]');
    if ((await coursesSection.count()) > 0) {
      await expect(coursesSection).toBeVisible();
    }
  });

  test("should navigate to login page", async ({ page }) => {
    await page.goto("/");

    // Look for login link/button
    const loginButton = page.locator(
      'a[href*="login"], button:has-text("Login"), a:has-text("Login")'
    );

    if ((await loginButton.count()) > 0) {
      await loginButton.first().click();
      await expect(page).toHaveURL(/.*login.*/);
    }
  });

  test("should be responsive on mobile", async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto("/");

    // Check if page loads properly on mobile
    await expect(page.locator("body")).toBeVisible();

    // Check if mobile navigation works (if exists)
    const mobileMenu = page.locator(
      '[data-testid="mobile-menu"], button[aria-label*="menu"]'
    );
    if ((await mobileMenu.count()) > 0) {
      await mobileMenu.click();
      // Check if menu opens
      await page.waitForTimeout(500);
    }
  });

  test("should have proper meta tags for SEO", async ({ page }) => {
    await page.goto("/");

    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute("content", /.+/);

    // Check if title is set
    await expect(page).toHaveTitle(/.+/);
  });
});
