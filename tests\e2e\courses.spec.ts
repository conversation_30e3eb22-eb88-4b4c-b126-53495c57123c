import { test, expect } from '@playwright/test'

test.describe('Course Pages', () => {
  test('should display course listing page', async ({ page }) => {
    await page.goto('/courses')
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Check if courses are displayed
    const courseCards = page.locator('[data-testid="course-card"], .course-card, article')
    
    // If courses exist, check their structure
    if (await courseCards.count() > 0) {
      const firstCourse = courseCards.first()
      await expect(firstCourse).toBeVisible()
      
      // Check for course title
      const courseTitle = firstCourse.locator('h1, h2, h3, [data-testid="course-title"]')
      if (await courseTitle.count() > 0) {
        await expect(courseTitle).toBeVisible()
      }
    }
  })

  test('should navigate to course detail page', async ({ page }) => {
    await page.goto('/courses')
    await page.waitForLoadState('networkidle')
    
    // Find and click on first course link
    const courseLinks = page.locator('a[href*="/courses/"]')
    
    if (await courseLinks.count() > 0) {
      const firstCourseLink = courseLinks.first()
      await firstCourseLink.click()
      
      // Should navigate to course detail page
      await expect(page).toHaveURL(/.*\/courses\/.*/)
      
      // Check if course detail content loads
      await page.waitForLoadState('networkidle')
      await expect(page.locator('body')).toBeVisible()
    }
  })

  test('should display course information correctly', async ({ page }) => {
    // Try to navigate to a course detail page
    await page.goto('/courses')
    await page.waitForLoadState('networkidle')
    
    const courseLinks = page.locator('a[href*="/courses/"]')
    
    if (await courseLinks.count() > 0) {
      await courseLinks.first().click()
      await page.waitForLoadState('networkidle')
      
      // Check for course title
      const title = page.locator('h1')
      if (await title.count() > 0) {
        await expect(title).toBeVisible()
      }
      
      // Check for course description
      const description = page.locator('[data-testid="course-description"], .description, p')
      if (await description.count() > 0) {
        await expect(description.first()).toBeVisible()
      }
      
      // Check for enrollment button
      const enrollButton = page.locator('button:has-text("Enroll"), a:has-text("Enroll"), button:has-text("Join")')
      if (await enrollButton.count() > 0) {
        await expect(enrollButton.first()).toBeVisible()
      }
    }
  })

  test('should handle course enrollment flow', async ({ page }) => {
    await page.goto('/courses')
    await page.waitForLoadState('networkidle')
    
    const courseLinks = page.locator('a[href*="/courses/"]')
    
    if (await courseLinks.count() > 0) {
      await courseLinks.first().click()
      await page.waitForLoadState('networkidle')
      
      // Look for enrollment button
      const enrollButton = page.locator('button:has-text("Enroll"), a:has-text("Enroll"), button:has-text("Join")')
      
      if (await enrollButton.count() > 0) {
        await enrollButton.first().click()
        
        // Should either redirect to login or payment
        await page.waitForTimeout(2000)
        
        const currentUrl = page.url()
        const isLoginPage = currentUrl.includes('login')
        const isPaymentPage = currentUrl.includes('payment') || currentUrl.includes('checkout')
        
        expect(isLoginPage || isPaymentPage).toBeTruthy()
      }
    }
  })

  test('should filter courses correctly', async ({ page }) => {
    await page.goto('/courses')
    await page.waitForLoadState('networkidle')
    
    // Look for filter controls
    const filterControls = page.locator('select, input[type="search"], [data-testid="filter"]')
    
    if (await filterControls.count() > 0) {
      const initialCourseCount = await page.locator('[data-testid="course-card"], .course-card, article').count()
      
      // Try to interact with first filter
      const firstFilter = filterControls.first()
      const tagName = await firstFilter.tagName()
      
      if (tagName === 'SELECT') {
        const options = firstFilter.locator('option')
        if (await options.count() > 1) {
          await firstFilter.selectOption({ index: 1 })
          await page.waitForTimeout(1000)
          
          // Check if course list updated
          const newCourseCount = await page.locator('[data-testid="course-card"], .course-card, article').count()
          // Course count might change or stay the same depending on filter
        }
      }
    }
  })

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/courses')
    await page.waitForLoadState('networkidle')
    
    // Check if page loads properly on mobile
    await expect(page.locator('body')).toBeVisible()
    
    // Check if course cards are properly displayed on mobile
    const courseCards = page.locator('[data-testid="course-card"], .course-card, article')
    if (await courseCards.count() > 0) {
      await expect(courseCards.first()).toBeVisible()
    }
  })
})
