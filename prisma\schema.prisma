generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String           @id
  name           String
  email          String
  emailVerified  Boolean
  image          String?
  createdAt      DateTime
  updatedAt      DateTime
  sessions       Session[]
  accounts       Account[]
  courses        Course[]
  enrollments    Enrollment[]
  lessonProgress LessonProgress[]

  stripeCustomerId String? @unique

  role       String?
  banned     Boolean?
  banReason  String?
  banExpires DateTime?

  @@unique([email])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  impersonatedBy String?

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

enum CourseLevel {
  Beginner
  Intermediate
  Advanced
}

enum CourseStatus {
  Draft
  Published
  Archived
}

model Course {
  id               String       @id @default(uuid())
  title            String
  smallDescription String
  description      String
  fileKey          String
  price            Int
  duration         Int
  category         String
  slug             String       @unique
  level            CourseLevel  @default(Beginner)
  status           CourseStatus @default(Draft)
  chapters         Chapter[]
  enrollments      Enrollment[]
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt

  stripePriceId String @unique

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String
}

model Chapter {
  id        String   @id @default(uuid())
  title     String
  position  Int
  lessons   Lesson[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId String
}

model Lesson {
  id             String           @id @default(uuid())
  title          String
  description    String?
  thumbnailKey   String?
  videoKey       String?
  position       Int
  lessonProgress LessonProgress[]
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  chapter   Chapter @relation(fields: [chapterId], references: [id], onDelete: Cascade)
  chapterId String
}

enum EnrollmentStatus {
  Pending
  Completed
  Cancelled
}

model Enrollment {
  id        String           @id @default(uuid())
  amount    Int
  status    EnrollmentStatus @default(Pending)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  courseId String

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@unique([courseId, userId])
}

model LessonProgress {
  id        String   @id @default(uuid())
  completed Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  lesson   Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  lessonId String

  @@unique([userId, lessonId])
}
