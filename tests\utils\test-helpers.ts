import { render, RenderOptions } from "@testing-library/react";
import { ReactElement } from "react";

// Mock data generators
export const mockUser = {
  id: "user-123",
  name: "Test User",
  email: "<EMAIL>",
  emailVerified: true,
  image: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  role: "user",
  banned: false,
  banReason: null,
  banExpires: null,
  stripeCustomerId: null,
};

export const mockCourse = {
  id: "course-123",
  title: "Test Course",
  smallDescription: "A test course",
  description: "This is a detailed description of the test course",
  fileKey: "courses/test-course.jpg",
  price: 5000,
  duration: 120,
  category: "Programming",
  slug: "test-course",
  level: "Beginner" as const,
  status: "Published" as const,
  stripePriceId: "price_test123",
  userId: "user-123",
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockChapter = {
  id: "chapter-123",
  title: "Test Chapter",
  position: 1,
  courseId: "course-123",
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockLesson = {
  id: "lesson-123",
  title: "Test Lesson",
  description: "A test lesson description",
  thumbnailKey: "lessons/test-lesson-thumb.jpg",
  videoKey: "lessons/test-lesson-video.mp4",
  position: 1,
  chapterId: "chapter-123",
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockEnrollment = {
  id: "enrollment-123",
  amount: 5000,
  status: "Completed" as const,
  courseId: "course-123",
  userId: "user-123",
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockLessonProgress = {
  id: "progress-123",
  completed: true,
  userId: "user-123",
  lessonId: "lesson-123",
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Test data builders
export const createMockCourseWithChapters = (overrides = {}) => ({
  ...mockCourse,
  chapters: [
    {
      ...mockChapter,
      lessons: [
        {
          ...mockLesson,
          lessonProgress: [mockLessonProgress],
        },
      ],
    },
  ],
  ...overrides,
});

export const createMockUserWithEnrollments = (overrides = {}) => ({
  ...mockUser,
  enrollments: [mockEnrollment],
  ...overrides,
});

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  // Add any custom options here
}

export const customRender = (
  ui: ReactElement,
  options?: CustomRenderOptions
) => {
  // You can add providers here if needed
  // const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  //   return (
  //     <ThemeProvider>
  //       <AuthProvider>
  //         {children}
  //       </AuthProvider>
  //     </ThemeProvider>
  //   )
  // }

  return render(ui, { ...options });
};

// Re-export everything from testing-library
export * from "@testing-library/react";
export { customRender as render };

// Utility functions for tests
export const waitFor = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const mockLocalStorage = () => {
  const store: Record<string, string> = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach((key) => delete store[key]);
    }),
  };
};

export const mockSessionStorage = () => {
  const store: Record<string, string> = {};

  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach((key) => delete store[key]);
    }),
  };
};

// Mock fetch for API tests
export const mockFetch = (response: any, ok = true) => {
  return jest.fn().mockResolvedValue({
    ok,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
    status: ok ? 200 : 400,
  });
};

// Environment variable helpers
export const setTestEnvVars = (vars: Record<string, string>) => {
  Object.keys(vars).forEach((key) => {
    process.env[key] = vars[key];
  });
};

export const clearTestEnvVars = (keys: string[]) => {
  keys.forEach((key) => {
    delete process.env[key];
  });
};
