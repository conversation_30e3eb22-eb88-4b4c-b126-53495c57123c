import { test, expect } from "@playwright/test";

test.describe("Authentication Flow", () => {
  test("should display login page correctly", async ({ page }) => {
    await page.goto("/login");

    // Check if login form exists
    await expect(page.locator("form")).toBeVisible();

    // Check for email input
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    await expect(emailInput).toBeVisible();

    // Check for submit button
    const submitButton = page.locator(
      'button[type="submit"], input[type="submit"]'
    );
    await expect(submitButton).toBeVisible();
  });

  test("should show validation errors for invalid email", async ({ page }) => {
    await page.goto("/login");

    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const submitButton = page.locator(
      'button[type="submit"], input[type="submit"]'
    );

    // Try to submit with invalid email
    await emailInput.fill("invalid-email");
    await submitButton.click();

    // Check for validation error (this might vary based on implementation)
    const errorMessage = page.locator(
      '[role="alert"], .error, [data-testid="error"]'
    );
    if ((await errorMessage.count()) > 0) {
      await expect(errorMessage).toBeVisible();
    }
  });

  test("should handle empty form submission", async ({ page }) => {
    await page.goto("/login");

    const submitButton = page.locator(
      'button[type="submit"], input[type="submit"]'
    );
    await submitButton.click();

    // Should show some form of validation
    // This could be browser validation or custom validation
    const emailInput = page.locator('input[type="email"], input[name="email"]');

    // Check if browser validation kicks in
    const validationMessage = await emailInput.evaluate(
      (el: HTMLInputElement) => el.validationMessage
    );
    if (validationMessage) {
      expect(validationMessage).toBeTruthy();
    }
  });

  test("should redirect to verify page after valid email submission", async ({
    page,
  }) => {
    await page.goto("/login");

    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const submitButton = page.locator(
      'button[type="submit"], input[type="submit"]'
    );

    // Submit with valid email
    await emailInput.fill("<EMAIL>");
    await submitButton.click();

    // Wait for navigation or success state
    await page.waitForTimeout(2000);

    // Check if redirected to verify page or shows success message
    const currentUrl = page.url();
    const hasVerifyInUrl = currentUrl.includes("verify");
    const hasSuccessMessage =
      (await page
        .locator(
          ':has-text("check your email"), :has-text("verification"), :has-text("OTP")'
        )
        .count()) > 0;

    expect(hasVerifyInUrl || hasSuccessMessage).toBeTruthy();
  });

  test("should handle OTP verification page", async ({ page }) => {
    // Navigate directly to verify page (assuming it exists)
    await page.goto("/verify-request");

    // Check if OTP input exists
    const otpInputs = page.locator('input[type="text"], input[type="number"]');
    if ((await otpInputs.count()) > 0) {
      await expect(otpInputs.first()).toBeVisible();
    }
  });

  test("should be accessible", async ({ page }) => {
    await page.goto("/login");

    // Check for proper form labels
    const emailInput = page.locator('input[type="email"], input[name="email"]');
    const emailLabel = page
      .locator("label[for], label")
      .filter({ has: emailInput });

    if ((await emailLabel.count()) > 0) {
      await expect(emailLabel).toBeVisible();
    }

    // Check for proper heading structure
    const mainHeading = page.locator("h1");
    if ((await mainHeading.count()) > 0) {
      await expect(mainHeading).toBeVisible();
    }
  });
});
