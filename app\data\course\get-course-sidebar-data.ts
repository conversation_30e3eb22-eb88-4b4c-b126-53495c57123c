import "server-only";
import { notFound } from "next/navigation";

import prisma from "@/lib/db";
import { requireUser } from "../user/require-user";

export async function getCourseSidebarData(slug: string) {
  const session = await requireUser();
  await new Promise((resolve) => setTimeout(resolve, 1000));

  const course = await prisma.course.findUnique({
    where: {
      slug: slug,
    },
    select: {
      id: true,
      title: true,
      description: true,
      smallDescription: true,
      fileKey: true,
      duration: true,
      level: true,
      category: true,
      slug: true,
      createdAt: true,
      user: {
        select: {
          name: true,
          email: true,
        },
      },
      chapters: {
        orderBy: {
          position: "asc",
        },
        select: {
          id: true,
          title: true,
          position: true,
          lessons: {
            orderBy: {
              position: "asc",
            },
            select: {
              id: true,
              title: true,
              position: true,
              description: true,
              lessonProgress: {
                where: {
                  userId: session.id,
                },
                select: {
                  completed: true,
                  lessonId: true,
                  id: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!course) {
    return notFound();
  }

  const enrollment = await prisma.enrollment.findUnique({
    where: {
      courseId_userId: {
        courseId: course.id,
        userId: session.id,
      },
    },
  });

  if (!enrollment || enrollment.status !== "Completed") {
    return notFound();
  }

  return {
    course,
    enrollment,
  };
}

export type CourseSidebarDataType = Awaited<
  ReturnType<typeof getCourseSidebarData>
>;
