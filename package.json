{"name": "inulpro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "postinstall": "prisma generate"}, "dependencies": {"@arcjet/inspect": "^1.0.0-beta.9", "@arcjet/ip": "^1.0.0-beta.9", "@arcjet/next": "^1.0.0-beta.9", "@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/s3-request-presigner": "^3.848.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.12.0", "@prisma/extension-accelerate": "^2.0.2", "@prisma/nextjs-monorepo-workaround-plugin": "^6.12.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.8", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/html": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "better-auth": "^1.2.12", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html-react-parser": "^5.2.6", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "next": "15.4.1", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "recharts": "^2.15.4", "slugify": "^1.6.6", "sonner": "^2.0.6", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.12", "@testing-library/react": "^16.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.5.2", "@playwright/test": "^1.49.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "eslint": "^9", "eslint-config-next": "15.4.1", "prisma": "^6.12.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}