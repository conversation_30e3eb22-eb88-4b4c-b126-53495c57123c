import { getS3PublicUrl } from '@/lib/s3-utils'

// Mock environment variable
const originalEnv = process.env
beforeEach(() => {
  jest.resetModules()
  process.env = { ...originalEnv }
  process.env.NEXT_PUBLIC_S3_BUCKET_NAME_IMAGES = 'test-bucket'
})

afterEach(() => {
  process.env = originalEnv
})

describe('S3 Utils', () => {
  describe('getS3PublicUrl', () => {
    it('should generate correct S3 public URL', () => {
      const key = 'images/test-image.jpg'
      const result = getS3PublicUrl(key)
      expect(result).toContain('test-bucket')
      expect(result).toContain('test-image.jpg')
    })

    it('should handle keys with leading slash', () => {
      const key = '/images/test-image.jpg'
      const result = getS3PublicUrl(key)
      expect(result).not.toMatch(/\/\/images/)
      expect(result).toContain('images/test-image.jpg')
    })

    it('should return empty string for empty key', () => {
      const result = getS3PublicUrl('')
      expect(result).toBe('')
    })

    it('should handle missing bucket name', () => {
      delete process.env.NEXT_PUBLIC_S3_BUCKET_NAME_IMAGES
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      const result = getS3PublicUrl('test-key')
      expect(result).toBe('')
      expect(consoleSpy).toHaveBeenCalledWith('NEXT_PUBLIC_S3_BUCKET_NAME_IMAGES not found')
      
      consoleSpy.mockRestore()
    })
  })
})
