import { otpRateLimiter, createRateLimitKey } from '@/lib/rate-limit'

describe('Rate Limiting', () => {
  beforeEach(() => {
    // Reset rate limiter before each test
    const testKey = createRateLimitKey('<EMAIL>')
    otpRateLimiter.reset(testKey)
  })

  describe('createRateLimitKey', () => {
    it('should create consistent rate limit key', () => {
      const email = '<EMAIL>'
      const key1 = createRateLimitKey(email)
      const key2 = createRateLimitKey(email)
      expect(key1).toBe(key2)
    })

    it('should create different keys for different emails', () => {
      const key1 = createRateLimitKey('<EMAIL>')
      const key2 = createRateLimitKey('<EMAIL>')
      expect(key1).not.toBe(key2)
    })
  })

  describe('otpRateLimiter', () => {
    it('should allow requests within limit', () => {
      const testKey = createRateLimitKey('<EMAIL>')
      
      // First request should be allowed
      const result1 = otpRateLimiter.isAllowed(testKey)
      expect(result1.allowed).toBe(true)
      
      // Second request should be allowed
      const result2 = otpRateLimiter.isAllowed(testKey)
      expect(result2.allowed).toBe(true)
    })

    it('should block requests exceeding limit', () => {
      const testKey = createRateLimitKey('<EMAIL>')
      
      // Make 5 requests (assuming limit is 5)
      for (let i = 0; i < 5; i++) {
        otpRateLimiter.isAllowed(testKey)
      }
      
      // 6th request should be blocked
      const result = otpRateLimiter.isAllowed(testKey)
      expect(result.allowed).toBe(false)
      expect(result.message).toContain('Too many requests')
    })

    it('should provide correct stats', () => {
      const testKey = createRateLimitKey('<EMAIL>')
      
      // Make one request
      otpRateLimiter.isAllowed(testKey)
      
      const stats = otpRateLimiter.getStats(testKey)
      expect(stats.requests).toBeGreaterThan(0)
      expect(stats.remaining).toBeLessThan(5) // Assuming limit is 5
      expect(stats.resetTime).toBeInstanceOf(Date)
    })

    it('should reset rate limit correctly', () => {
      const testKey = createRateLimitKey('<EMAIL>')
      
      // Make requests to consume limit
      for (let i = 0; i < 5; i++) {
        otpRateLimiter.isAllowed(testKey)
      }
      
      // Reset and try again
      otpRateLimiter.reset(testKey)
      const result = otpRateLimiter.isAllowed(testKey)
      expect(result.allowed).toBe(true)
    })
  })
})
