import { render, screen } from '@testing-library/react'
import { Skeleton } from '@/components/ui/skeleton'

describe('Skeleton Component', () => {
  it('should render skeleton element', () => {
    render(<Skeleton data-testid="skeleton" />)
    expect(screen.getByTestId('skeleton')).toBeInTheDocument()
  })

  it('should have default skeleton classes', () => {
    render(<Skeleton data-testid="skeleton" />)
    const skeleton = screen.getByTestId('skeleton')
    
    expect(skeleton).toHaveClass('bg-accent')
    expect(skeleton).toHaveClass('animate-pulse')
    expect(skeleton).toHaveClass('rounded-md')
  })

  it('should apply custom className', () => {
    render(<Skeleton className="h-4 w-full" data-testid="skeleton" />)
    const skeleton = screen.getByTestId('skeleton')
    
    expect(skeleton).toHaveClass('h-4')
    expect(skeleton).toHaveClass('w-full')
    expect(skeleton).toHaveClass('bg-accent') // Should still have default classes
  })

  it('should have correct data attributes', () => {
    render(<Skeleton data-testid="skeleton" />)
    expect(screen.getByTestId('skeleton')).toHaveAttribute('data-slot', 'skeleton')
  })

  it('should accept additional props', () => {
    render(<Skeleton id="test-skeleton" data-testid="skeleton" />)
    expect(screen.getByTestId('skeleton')).toHaveAttribute('id', 'test-skeleton')
  })

  it('should render as div element', () => {
    render(<Skeleton data-testid="skeleton" />)
    expect(screen.getByTestId('skeleton').tagName).toBe('DIV')
  })
})
