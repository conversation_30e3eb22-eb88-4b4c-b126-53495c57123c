import { renderHook } from '@testing-library/react'
import { useCourseProgress } from '@/hooks/use-course-progress'

// Mock course data type
const mockCourseData = {
  chapters: [
    {
      id: 'chapter-1',
      lessons: [
        {
          id: 'lesson-1',
          lessonProgress: [
            { lessonId: 'lesson-1', completed: true }
          ]
        },
        {
          id: 'lesson-2',
          lessonProgress: [
            { lessonId: 'lesson-2', completed: false }
          ]
        }
      ]
    },
    {
      id: 'chapter-2',
      lessons: [
        {
          id: 'lesson-3',
          lessonProgress: [
            { lessonId: 'lesson-3', completed: true }
          ]
        },
        {
          id: 'lesson-4',
          lessonProgress: []
        }
      ]
    }
  ]
}

describe('useCourseProgress Hook', () => {
  it('should calculate correct progress for course with mixed completion', () => {
    const { result } = renderHook(() => 
      useCourseProgress({ courseData: mockCourseData })
    )

    expect(result.current.totalLessons).toBe(4)
    expect(result.current.completedLessons).toBe(2)
    expect(result.current.progressPercentage).toBe(50)
  })

  it('should handle course with no lessons', () => {
    const emptyCourseData = {
      chapters: []
    }

    const { result } = renderHook(() => 
      useCourseProgress({ courseData: emptyCourseData })
    )

    expect(result.current.totalLessons).toBe(0)
    expect(result.current.completedLessons).toBe(0)
    expect(result.current.progressPercentage).toBe(0)
  })

  it('should handle course with all lessons completed', () => {
    const completedCourseData = {
      chapters: [
        {
          id: 'chapter-1',
          lessons: [
            {
              id: 'lesson-1',
              lessonProgress: [
                { lessonId: 'lesson-1', completed: true }
              ]
            },
            {
              id: 'lesson-2',
              lessonProgress: [
                { lessonId: 'lesson-2', completed: true }
              ]
            }
          ]
        }
      ]
    }

    const { result } = renderHook(() => 
      useCourseProgress({ courseData: completedCourseData })
    )

    expect(result.current.totalLessons).toBe(2)
    expect(result.current.completedLessons).toBe(2)
    expect(result.current.progressPercentage).toBe(100)
  })

  it('should handle course with no completed lessons', () => {
    const incompleteCourseData = {
      chapters: [
        {
          id: 'chapter-1',
          lessons: [
            {
              id: 'lesson-1',
              lessonProgress: []
            },
            {
              id: 'lesson-2',
              lessonProgress: [
                { lessonId: 'lesson-2', completed: false }
              ]
            }
          ]
        }
      ]
    }

    const { result } = renderHook(() => 
      useCourseProgress({ courseData: incompleteCourseData })
    )

    expect(result.current.totalLessons).toBe(2)
    expect(result.current.completedLessons).toBe(0)
    expect(result.current.progressPercentage).toBe(0)
  })

  it('should recalculate when course data changes', () => {
    const initialData = {
      chapters: [
        {
          id: 'chapter-1',
          lessons: [
            {
              id: 'lesson-1',
              lessonProgress: [
                { lessonId: 'lesson-1', completed: false }
              ]
            }
          ]
        }
      ]
    }

    const { result, rerender } = renderHook(
      ({ courseData }) => useCourseProgress({ courseData }),
      { initialProps: { courseData: initialData } }
    )

    expect(result.current.progressPercentage).toBe(0)

    // Update with completed lesson
    const updatedData = {
      chapters: [
        {
          id: 'chapter-1',
          lessons: [
            {
              id: 'lesson-1',
              lessonProgress: [
                { lessonId: 'lesson-1', completed: true }
              ]
            }
          ]
        }
      ]
    }

    rerender({ courseData: updatedData })

    expect(result.current.progressPercentage).toBe(100)
  })
})
